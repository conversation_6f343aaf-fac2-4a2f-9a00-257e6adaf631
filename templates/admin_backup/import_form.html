{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block extrahead %}{{ block.super }}
<script type="text/javascript" src="{% url 'admin:jsi18n' %}"></script>
{{ media }}
{% endblock %}

{% block extrastyle %}{{ block.super }}<link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}">{% endblock %}

{% block coltype %}colM{% endblock %}

{% block bodyclass %}{{ block.super }} app-{{ opts.app_label }} model-{{ opts.model_name }} change-form{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
&rsaquo; <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
&rsaquo; {% trans 'Import' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <div class="module">
        <h2>{% trans "Import" %} {{ opts.verbose_name_plural }}</h2>
        
        <div class="form-row">
            <p>{% trans "Upload an Excel file to import data. The file should have one or more sheets, each corresponding to a model." %}</p>
            <p>{% trans "You can download a template file to see the required format." %}</p>
        </div>
        
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <div class="form-row">
                <div>
                    <label for="id_file">{% trans "File to import:" %}</label>
                    <input type="file" name="file" id="id_file" required>
                </div>
            </div>
            
            <div class="submit-row">
                <input type="submit" value="{% trans 'Import' %}" class="default" name="_import">
            </div>
        </form>
        
        {% if templates %}
            <h2>{% trans "Available Templates" %}</h2>
            <table>
                <thead>
                    <tr>
                        <th>{% trans "Name" %}</th>
                        <th>{% trans "Description" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for template in templates %}
                        <tr>
                            <td>{{ template.name }}</td>
                            <td>{{ template.description|default:"-" }}</td>
                            <td>
                                <a href="{% url 'data_import_export:download_import_template' template.id %}" class="button">
                                    {% trans "Download" %}
                                </a>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="form-row">
                <p>{% trans "No templates available for this model." %}</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
