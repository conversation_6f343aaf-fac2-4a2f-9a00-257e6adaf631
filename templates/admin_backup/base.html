{% load i18n static %}
<!DOCTYPE html>
{% get_current_language as LANGUAGE_CODE %}{% get_current_language_bidi as LANGUAGE_BIDI %}
<html lang="{{ LANGUAGE_CODE|default:"en-us" }}" dir="{{ LANGUAGE_BIDI|yesno:'rtl,ltr,auto' }}">
<head>
<title>{% block title %}{% endblock %}</title>
<link rel="stylesheet" href="{% block stylesheet %}{% static "admin/css/base.css" %}{% endblock %}">
{% block dark-mode-vars %}
  <link rel="stylesheet" href="{% static "admin/css/dark_mode.css" %}">
  <script src="{% static "admin/js/theme.js" %}" defer></script>
{% endblock %}
{% if not is_popup and is_nav_sidebar_enabled %}
  <link rel="stylesheet" href="{% static "admin/css/nav_sidebar.css" %}">
  <script src="{% static 'admin/js/nav_sidebar.js' %}" defer></script>
{% endif %}
{% block extrastyle %}{% endblock %}
{% if LANGUAGE_BIDI %}<link rel="stylesheet" href="{% block stylesheet_rtl %}{% static "admin/css/rtl.css" %}{% endblock %}">{% endif %}
{% block extrahead %}{% endblock %}
{% block responsive %}
    <meta name="viewport" content="user-scalable=no, width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <link rel="stylesheet" href="{% static "admin/css/responsive.css" %}">
    {% if LANGUAGE_BIDI %}<link rel="stylesheet" href="{% static "admin/css/responsive_rtl.css" %}">{% endif %}
{% endblock %}
{% block blockbots %}<meta name="robots" content="NONE,NOARCHIVE">{% endblock %}
</head>

<body class="{% if is_popup %}popup {% endif %}{% block bodyclass %}{% endblock %}" data-admin-utc-offset="{% now "Z" %}">
<a href="#content-start" class="skip-to-content-link">{% translate 'Skip to main content' %}</a>
<!-- Container -->
<div id="container">

    {% if not is_popup %}
    <!-- Header -->
    {% block header %}
    <div id="header">
        <div id="branding">
        {% block branding %}{% endblock %}
        </div>
        {% block usertools %}
        {% if has_permission %}
        <div id="user-tools">
            {% block welcome-msg %}
                {% translate 'Welcome,' %}
                <strong>{% firstof user.get_short_name user.get_username %}</strong>.
            {% endblock %}
            {% block userlinks %}
                {% if site_url %}
                    <a href="{{ site_url }}">{% translate 'View site' %}</a> /
                {% endif %}
                {% if user.is_active and user.is_staff %}
                    {% url 'django-admindocs-docroot' as docsroot %}
                    {% if docsroot %}
                        <a href="{{ docsroot }}">{% translate 'Documentation' %}</a> /
                    {% endif %}
                {% endif %}
                {% if user.has_usable_password %}
                    <a href="{% url 'admin:password_change' %}">{% translate 'Change password' %}</a> /
                {% endif %}
                <a href="{% url 'admin:logout' %}">{% translate 'Log out' %}</a>
            {% endblock %}
        </div>
        {% endif %}
        {% endblock %}
        {% block nav-global %}{% endblock %}
    </div>
    {% endblock %}
    {% endif %}

    <div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
    {% if title %} &rsaquo; {{ title }}{% endif %}
    </div>

    {% block messages %}
        {% if messages %}
        <ul class="messagelist">{% for message in messages %}
            <li{% if message.tags %} class="{{ message.tags }}"{% endif %}>{{ message|capfirst }}</li>
        {% endfor %}</ul>
        {% endif %}
    {% endblock messages %}

    <!-- Content -->
    <div id="content" class="{% block coltype %}colM{% endblock %}">
        {% block pretitle %}{% endblock %}
        {% block content_title %}{% if title %}<h1>{{ title }}</h1>{% endif %}{% endblock %}
        {% block content_subtitle %}{% if subtitle %}<h2>{{ subtitle }}</h2>{% endif %}{% endblock %}
        {% block content %}
            {% block object-tools %}{% endblock %}
            {{ content }}
        {% endblock %}
        {% block sidebar %}{% endblock %}
        <br class="clear">
    </div>
</div>
<!-- END Container -->
</body>
</html> 