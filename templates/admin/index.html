{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_list %}

{% block extrahead %}
    {{ block.super }}
    <style>
        /* تحسين الصفحة الرئيسية */
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .quick-actions {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .quick-actions h3 {
            color: #667eea;
            text-align: center;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .action-btn {
            display: block;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            color: white;
            text-decoration: none;
        }
        
        .action-btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }
        
        .action-btn.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        
        .action-btn.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
        
        .recent-activity {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .recent-activity h3 {
            color: #667eea;
            text-align: center;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .activity-item {
            padding: 10px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            color: #667eea;
            margin-left: 10px;
            font-size: 1.2rem;
        }
        
        .activity-text {
            flex: 1;
            text-align: right;
        }
        
        .activity-time {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .dashboard-stats {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
            
            .stat-number {
                font-size: 2rem;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div id="content-main">
    <!-- إحصائيات سريعة -->
    <div class="dashboard-stats">
        <div class="stat-card">
            <div class="stat-icon">👥</div>
            <div class="stat-number" id="customers-count">-</div>
            <div class="stat-label">إجمالي العملاء</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">📋</div>
            <div class="stat-number" id="orders-count">-</div>
            <div class="stat-label">إجمالي الطلبات</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">🔍</div>
            <div class="stat-number" id="inspections-count">-</div>
            <div class="stat-label">المعاينات المعلقة</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">🏭</div>
            <div class="stat-number" id="manufacturing-count">-</div>
            <div class="stat-label">التصنيع النشط</div>
        </div>
    </div>
    
    <!-- إجراءات سريعة -->
    <div class="quick-actions">
        <h3>🚀 الإجراءات السريعة</h3>
        <div class="action-buttons">
            <a href="{% url 'admin:customers_customer_add' %}" class="action-btn">
                👤 إضافة عميل جديد
            </a>
            <a href="{% url 'admin:orders_order_add' %}" class="action-btn">
                📋 إنشاء طلب جديد
            </a>
            <a href="{% url 'admin:inspections_inspection_add' %}" class="action-btn warning">
                🔍 جدولة معاينة
            </a>
            <a href="{% url 'admin:manufacturing_manufacturingorder_add' %}" class="action-btn info">
                🏭 أمر تصنيع جديد
            </a>
            <a href="{% url 'admin:backup_system_backupjob_changelist' %}" class="action-btn danger">
                💾 النسخ الاحتياطي
            </a>
            <a href="{% url 'admin:reports_report_changelist' %}" class="action-btn info">
                📊 عرض التقارير
            </a>
        </div>
    </div>
    
    <!-- قائمة التطبيقات -->
    {% if app_list %}
        <div class="app-list">
            {% for app in app_list %}
                <div class="module">
                    <h2>
                        <a href="{{ app.app_url }}" class="section" title="{% blocktranslate with name=app.name %}Models in the {{ name }} application{% endblocktranslate %}">
                            {{ app.name }}
                        </a>
                    </h2>
                    
                    {% if app.models %}
                        <table>
                            <thead>
                                <tr>
                                    <th>النموذج</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for model in app.models %}
                                    <tr>
                                        <td>{{ model.name }}</td>
                                        <td>
                                            {% if model.admin_url %}
                                                <a href="{{ model.admin_url }}" class="changelink">{% translate 'Change' %}</a>
                                            {% endif %}
                                            {% if model.add_url %}
                                                <a href="{{ model.add_url }}" class="addlink">{% translate 'Add' %}</a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    {% else %}
        <p>{% translate "You don't have permission to view or edit anything." %}</p>
    {% endif %}
</div>

<script>
// تحميل الإحصائيات بشكل ديناميكي
document.addEventListener('DOMContentLoaded', function() {
    // محاكاة تحميل الإحصائيات (يمكن استبدالها بـ AJAX call)
    setTimeout(function() {
        document.getElementById('customers-count').textContent = '{{ customers_count|default:"0" }}';
        document.getElementById('orders-count').textContent = '{{ orders_count|default:"0" }}';
        document.getElementById('inspections-count').textContent = '{{ pending_inspections|default:"0" }}';
        document.getElementById('manufacturing-count').textContent = '{{ active_manufacturing|default:"0" }}';
    }, 500);
    
    // إضافة تأثيرات تفاعلية
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
{% endblock %}
